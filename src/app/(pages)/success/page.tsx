'use client';

import { Loader, ServiceNavigationMenuDesktop } from '@/src/app/_components';
import { SuccessContent } from '@/src/app/_components/Pages/Success/SuccessContent';
import { Suspense } from 'react';

export default function SuccessPage() {
  return (
    <Suspense fallback={<Loader />}>
      <div className="relative z-10 mx-auto mb-20 mt-16 max-w-7xl px-8 py-2 md:py-8 lg:px-12">
        <SuccessContent />
        <section className="relative z-40 mx-auto my-16 max-w-7xl border-t-gray-200 px-8 lg:px-12">
          <h2 className="my-8 text-3xl font-bold text-muted-foreground">
            Quer agendar outro serviço?
          </h2>
          <div className="py-10 2xl:-ml-40">
            <ServiceNavigationMenuDesktop
              className="w-full"
              containerClassName="grid-cols-2 md:grid-cols-4 gap-x-10 gap-y-6"
              categoryClassName="mb-6"
              categoryTitleClassName="flex items-center gap-2 mb-2"
              subcategoryListClassName="ml-7 space-y-3"
              subcategoryLinkClassName="text-sm font-medium text-muted-foreground hover:text-gray-900"
            />
          </div>
        </section>{' '}
      </div>
    </Suspense>
  );
}
