'use client';

import { <PERSON><PERSON>, Icon, IconName, Loader, Separator } from '@/src/app/_components';
import { MarkdownRenderer } from '@/src/app/_components/Pages/Service/MarkdownRenderer';
import {
  Category,
  Service as ContextService,
  useServiceContext,
} from '@/src/app/_context/ServiceContext';
import { categories } from '@/src/app/_data/categories';
import { useInputFormat, useOrderData, useTrackPurchaseEvent } from '@/src/app/_hooks/';
import { ApiOrderResponse } from '@/src/app/_interfaces';
import { formatDate, formatPeriod } from '@/src/app/_utils/';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { useCallback, useEffect } from 'react';

// Props interface for the SuccessContent component
interface SuccessContentProps {
  orderIdentifier?: string;
  servicesOverride?: Category[];
}

// Client component that uses useSearchParams
export function SuccessContent({
  orderIdentifier: propOrderIdentifier,
  servicesOverride,
}: SuccessContentProps = {}) {
  const searchParams = useSearchParams();
  // Check for both parameter names for backward compatibility
  const uuid = searchParams.get('uuid');
  const orderId = searchParams.get('orderId');
  // Use the first one that's available or the prop value if provided
  const orderIdentifier = propOrderIdentifier || uuid || orderId;

  const { capitalizeWords } = useInputFormat();
  const { services: contextServices } = useServiceContext();
  const { trackPurchaseEvent } = useTrackPurchaseEvent();

  // Use servicesOverride for tests if provided, otherwise use context services
  const services = servicesOverride || contextServices;

  // Keep the refactored hook usage
  const { orderData, isLoading, error } = useOrderData({ orderId: orderIdentifier });

  // Tracking principal de purchase, verifica se o evento ja foi enviado para evitar triggers desnecessários do evento.
  const handlePurchaseTracking = useCallback(
    (orderData: ApiOrderResponse) => {
      const serviceId = orderData?.service?.id;
      if (!serviceId) return;

      const purchaseKey = `purchase_sent_${serviceId}`;

      const alreadyTracked = sessionStorage.getItem(purchaseKey);

      if (!alreadyTracked) {
        // Pass the orderIdentifier as transaction_id to prevent duplicate events
        // Only pass orderIdentifier if it's not null
        trackPurchaseEvent(orderData, orderIdentifier || undefined);
        sessionStorage.setItem(purchaseKey, 'true');
      }
    },
    [trackPurchaseEvent, orderIdentifier]
  );

  // Combined useEffect for all analytics tracking to ensure hooks are called in the same order
  useEffect(() => {
    if (orderData?.service?.id) {
      handlePurchaseTracking(orderData);
    }
  }, [orderData, handlePurchaseTracking]);

  if (isLoading) {
    return <Loader />;
  }

  if (error) {
    return (
      <div className="container mx-auto max-w-4xl px-4 py-12">
        <div className="rounded-lg border border-red-200 bg-red-50 p-8 text-center">
          <h1 className="mb-4 text-2xl font-bold text-red-700">Erro</h1>
          <p className="mb-6 text-red-600">{error}</p>
          <Button asChild>
            <Link href="/">Voltar para a página inicial</Link>
          </Button>
        </div>
      </div>
    );
  }

  if (!orderData) {
    return null;
  }

  // Helper function to get category and subcategory info
  const getCategoryInfo = () => {
    if (!orderData?.service?.slug) return null;

    const matchedCategory = services?.find((category: Category) =>
      category.subcategories?.some((sub) =>
        sub.services?.some((service: ContextService) => service.slug === orderData?.service?.slug)
      )
    );

    const matchedSubcategory = matchedCategory?.subcategories?.find((sub) =>
      sub.services?.some((service: ContextService) => service.slug === orderData?.service?.slug)
    );

    return { matchedCategory, matchedSubcategory };
  };

  // Helper function to get the category icon
  const getCategoryIcon = () => {
    const categoryInfo = getCategoryInfo();
    if (!categoryInfo?.matchedCategory?.slug) return 'Wrench'; // Default fallback

    const categoryData = categories.find((cat) => cat.id === categoryInfo.matchedCategory?.slug);
    return categoryData?.icon || 'Wrench'; // Fallback to wrench if not found
  };

  const categoryInfo = getCategoryInfo();
  const categoryIcon = getCategoryIcon();

  return (
    <div className="flex flex-col gap-8">
      <div className="space-y-10">
        {/* Header Section */}
        <div className="flex-col items-start">
          <Icon
            name="CircleCheck"
            className="mb-4 h-12 w-12 flex-shrink-0 text-[#FCC800]"
            strokeWidth={2}
          />
          <div className="flex flex-col gap-6 lg:flex-row lg:gap-8">
            <h1 className="mb-4 text-3xl font-bold leading-tight text-[#1E293B] lg:w-1/2">
              Pronto, <br />
              serviço agendado!
            </h1>

            {/* Service Summary */}
            <div className="flex items-start gap-3 lg:w-1/2">
              <div className="flex items-center gap-2 pt-1">
                <Icon
                  name={categoryIcon as IconName}
                  className="h-5 w-5 text-[#020618]"
                  strokeWidth={2}
                />
              </div>
              <div className="flex-1">
                <h2 className="text-xl font-semibold text-[#020618]">{orderData?.service?.name}</h2>
                <p className="text-sm font-medium text-[#62748E]">
                  {categoryInfo?.matchedCategory?.name} → {categoryInfo?.matchedSubcategory?.name}
                </p>
                <p className="text-sm font-medium text-[#62748E]">
                  {formatDate(orderData?.appointment?.date || '')} -{' '}
                  {formatPeriod(orderData?.appointment?.period || '')}
                </p>
                <p className="text-sm font-medium text-[#62748E]">
                  {orderData?.address?.street}, {orderData?.address?.numberAd} -{' '}
                  {orderData?.address?.complement}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Separator Line */}
        <Separator className="border-[#E2E8F0]" />

        {/* Next Steps Section */}
        <div className="space-y-6">
          <h2 className="text-2xl font-bold text-[#1E293B]">Próximos passos</h2>

          <div className="space-y-4">
            {/* Step 1 */}
            <div className="rounded-lg border border-[#E2E8F0] p-6">
              <div className="flex items-start gap-2">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-[#62748E] text-white">
                  <span className="text-xl font-black">1</span>
                </div>
                <p className="text-base font-medium text-[#62748E]">
                  Você receberá um email de confirmação com todos os detalhes do agendamento e do
                  serviço.
                </p>
              </div>
            </div>

            {/* Step 2 */}
            <div className="rounded-lg border border-[#E2E8F0] p-6">
              <div className="flex items-start gap-2">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-[#62748E] text-white">
                  <span className="text-xl font-black">2</span>
                </div>
                <p className="text-base font-medium text-[#62748E]">
                  A {capitalizeWords(orderData?.service?.provider?.name || 'Europ Assistance')}{' '}
                  entrará em contato para compartilhar os detalhes e tirar eventuais dúvidas.
                </p>
              </div>
            </div>

            {/* Step 3 */}
            <div className="space-y-6 rounded-lg border border-[#E2E8F0] p-6">
              <div className="flex items-start gap-2">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-[#62748E] text-white">
                  <span className="text-xl font-black">3</span>
                </div>
                <p className="text-base font-medium text-[#62748E]">
                  Prepare-se para receber o serviço no dia e horário agendados.
                </p>
              </div>

              {/* Additional info for step 3 */}
              <div className="ml-6 space-y-4 text-base leading-7 text-[#62748E] sm:ml-10">
                <p>É importante que o produto já esteja disponível para o conserto;</p>
                <p>
                  Necessária a presença de um responsável maior de 18 anos. Caso contrário, será
                  preciso reagendar pelo telefone 0800 202 4011 da Central de Atendimento da Europ
                  Assistance;
                </p>
                <p>
                  Verifique a disponibilidade das conexões hidráulicas (fornecimento e saída de
                  água);
                </p>
                <p>
                  O cliente deve verificar se o produto está dentro do prazo de garantia do
                  fabricante ou assistência técnica, sob risco de perda de garantia caso execute o
                  serviço com terceiros.
                </p>
              </div>
            </div>

            {/* Reminder Alert */}
            <div className="rounded-lg border border-[#E2E8F0] bg-[#F1F5F9] p-4">
              <div className="flex items-start gap-4">
                <Icon
                  name="AlertCircle"
                  className="mt-0.5 h-5 w-5 text-[#62748E]"
                  strokeWidth={2}
                />
                <p className="text-base font-medium text-[#62748E]">
                  Lembre-se: a eventual aquisição das peças é de responsabilidade do cliente, e não
                  estão inclusas no valor.
                </p>
              </div>
            </div>

            {/* Reschedule Alert */}
            <div className="rounded-lg border border-[#E2E8F0] bg-[#F1F5F9] p-4">
              <div className="flex items-start gap-4">
                <Icon name="CalendarX2" className="mt-0.5 h-5 w-5 text-[#62748E]" strokeWidth={2} />
                <p className="text-base font-medium text-[#62748E]">
                  Precisou reagendar ou cancelar? <br />
                  Entre em contato com a Central de Atendimento da Europ pelo telefone 0800 202
                  4011.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Appointment Details Section */}
        <div className="space-y-6">
          <h2 className="text-2xl font-semibold text-[#020618]">Detalhes do agendamento</h2>

          <div className="space-y-8">
            {/* Service Details */}
            <div className="flex items-start gap-3">
              <Icon
                name={categoryIcon as IconName}
                className="mt-0.5 h-5 w-5 text-[#020618]"
                strokeWidth={2}
              />
              <div className="flex-1">
                <h3 className="text-xl font-semibold text-[#020618]">{orderData?.service?.name}</h3>
                <p className="text-sm font-medium text-[#62748E]">
                  {categoryInfo?.matchedCategory?.name} → {categoryInfo?.matchedSubcategory?.name}
                </p>
              </div>
            </div>

            <Separator className="border-[#E2E8F0]" />

            {/* Date and Address */}
            <div className="space-y-8 lg:grid lg:grid-cols-2 lg:gap-8 lg:space-y-0">
              <div className="space-y-6">
                {/* Date */}
                <div className="flex items-start gap-3">
                  <Icon name="Calendar" className="mt-0.5 h-5 w-5 text-[#62748E]" strokeWidth={2} />
                  <div>
                    <h4 className="text-base font-semibold text-[#62748E]">Data e Horário</h4>
                    <p className="text-sm font-medium text-[#62748E]">
                      {formatDate(orderData?.appointment?.date || '')} -{' '}
                      {formatPeriod(orderData?.appointment?.period || '')}
                    </p>
                  </div>
                </div>

                {/* Address */}
                <div className="flex items-start gap-3">
                  <Icon name="MapPin" className="mt-0.5 h-5 w-5 text-[#62748E]" strokeWidth={2} />
                  <div>
                    <h4 className="text-base font-semibold text-[#62748E]">Endereço</h4>
                    <p className="text-sm font-medium text-[#62748E]">
                      {orderData?.address?.street}, {orderData?.address?.numberAd} -{' '}
                      {orderData?.address?.complement}
                    </p>
                    <p className="text-sm font-medium text-[#62748E]">
                      {orderData?.address?.neighborhood} - {orderData?.address?.cityName}/
                      {orderData?.address?.uf}
                    </p>
                    <p className="text-sm font-medium text-[#62748E]">
                      CEP: {orderData?.address?.zipCode}
                    </p>
                  </div>
                </div>
              </div>

              <div className="space-y-6">
                {/* Personal Info */}
                <div className="flex items-start gap-3">
                  <Icon name="User" className="mt-0.5 h-5 w-5 text-[#62748E]" strokeWidth={2} />
                  <div>
                    <h4 className="text-base font-semibold text-[#62748E]">
                      Dados pessoais do contratante
                    </h4>
                    <p className="text-sm font-medium text-[#62748E]">
                      Nome: {capitalizeWords(orderData?.customer?.fullName || '')}
                    </p>
                    <p className="text-sm font-medium text-[#62748E]">
                      Celular: {orderData?.customer?.phone || ''}
                    </p>
                    <p className="text-sm font-medium text-[#62748E]">
                      E-mail: {orderData?.customer?.email || ''}
                    </p>
                    <p className="text-sm font-medium text-[#62748E]">
                      CPF: {orderData?.customer?.document || ''}
                    </p>
                  </div>
                </div>

                {/* Payment */}
                <div className="flex items-start gap-3">
                  <Icon
                    name="CreditCard"
                    className="mt-0.5 h-5 w-5 text-[#62748E]"
                    strokeWidth={2}
                  />
                  <div>
                    <h4 className="text-base font-semibold text-[#62748E]">Pagamento</h4>
                    <p className="text-sm font-medium text-[#62748E]">
                      Total pago: R${' '}
                      {orderData?.service?.price?.finalPrice?.toFixed(2)?.replace('.', ',') || ''}
                    </p>
                    <p className="text-sm font-medium text-[#62748E]">
                      Método de pagamento:{' '}
                      {orderData?.payment?.method === 'Credit Card'
                        ? 'Cartão de crédito'
                        : orderData?.payment?.method || ''}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <Separator className="border-[#E2E8F0]" />

            {/* What's Included */}
            <div className="space-y-4">
              <div className="flex items-center gap-4">
                <Icon name="CircleCheck" className="h-6 w-6 text-black" strokeWidth={2} />
                <h3 className="text-xl font-semibold text-[#020618]">O que está incluso</h3>
              </div>
              <div className="text-base leading-7 text-[#62748E]">
                {orderData?.service?.details && (
                  <MarkdownRenderer markdown={orderData?.service?.details || ''} />
                )}
              </div>

              {/* Included Services Reminder */}
              <div className="rounded-lg border border-[#E2E8F0] p-4">
                <div className="flex items-start gap-4">
                  <Icon
                    name="AlertCircle"
                    className="mt-0.5 h-5 w-5 text-[#62748E]"
                    strokeWidth={2}
                  />
                  <p className="text-base font-medium text-[#62748E]">
                    A eventual aquisição das peças é de responsabilidade do cliente, e não estão
                    inclusas no valor.
                  </p>
                </div>
              </div>
            </div>

            <Separator className="border-[#E2E8F0]" />

            {/* Restrictions */}
            <div className="space-y-4">
              <div className="flex items-center gap-4">
                <Icon name="Ban" className="h-6 w-6 text-black" strokeWidth={2} />
                <h3 className="text-xl font-semibold text-[#020618]">Restrições</h3>
              </div>
              <div className="rounded-lg bg-white p-4">
                <div className="text-base leading-7 text-[#62748E]">
                  {orderData?.service?.serviceLimits && (
                    <MarkdownRenderer markdown={orderData?.service?.serviceLimits || ''} />
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
